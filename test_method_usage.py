#!/usr/bin/env python3
"""
测试 _get_graph_type_and_seq_limit 方法是否被使用
"""

import sys
import os
import ast
import inspect

# 添加 vllm 路径
sys.path.insert(0, '/home/<USER>')

def find_method_calls_in_file(file_path, method_name):
    """在文件中查找方法调用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查直接调用
        if f".{method_name}(" in content or f"self.{method_name}(" in content:
            return True
            
        # 检查字符串引用
        if f'"{method_name}"' in content or f"'{method_name}'" in content:
            return True
            
        # 使用 AST 进行更深入的分析
        try:
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.Attribute) and node.attr == method_name:
                    return True
                if isinstance(node, ast.Call) and hasattr(node.func, 'attr') and node.func.attr == method_name:
                    return True
        except:
            pass
            
        return False
    except:
        return False

def scan_codebase_for_method_usage():
    """扫描整个代码库查找方法使用"""
    method_name = "_get_graph_type_and_seq_limit"
    found_usage = []
    
    # 扫描所有 Python 文件
    for root, dirs, files in os.walk('/home/<USER>'):
        # 跳过一些不相关的目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'build', 'dist']]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                if find_method_calls_in_file(file_path, method_name):
                    found_usage.append(file_path)
    
    return found_usage

def test_method_functionality():
    """测试方法的功能是否可以被其他方法替代"""
    try:
        from vllm.worker.model_runner import ModelRunner
        
        # 创建一个模拟的 ModelRunner 实例来测试逻辑
        class MockModelRunner:
            def __init__(self):
                self.short_seq_len_to_capture = 8192
                self.max_seq_len_to_capture = 16384
            
            def _get_graph_type_for_execution(self, batch_size: int, max_decode_seq_len: int) -> str:
                if self.max_seq_len_to_capture <= self.short_seq_len_to_capture:
                    return "original"
                
                if batch_size > 64 or max_decode_seq_len >= self.short_seq_len_to_capture:
                    return "original"
                else:
                    return "short"
            
            def _get_graph_type_and_seq_limit(self, batch_size: int, max_decode_seq_len: int) -> tuple[str, int]:
                """原始方法"""
                graph_type = self._get_graph_type_for_execution(batch_size, max_decode_seq_len)
                if graph_type == "short":
                    return graph_type, self.short_seq_len_to_capture
                else:  # "original"
                    return graph_type, self.max_seq_len_to_capture
            
            def alternative_implementation(self, batch_size: int, max_decode_seq_len: int) -> tuple[str, int]:
                """替代实现 - 不使用 _get_graph_type_and_seq_limit"""
                graph_type = self._get_graph_type_for_execution(batch_size, max_decode_seq_len)
                if graph_type == "short":
                    return graph_type, self.short_seq_len_to_capture
                else:
                    return graph_type, self.max_seq_len_to_capture
        
        mock = MockModelRunner()
        
        # 测试用例
        test_cases = [
            (32, 4000),
            (64, 8000),
            (128, 12000),
            (32, 10000),
        ]
        
        print("比较原始方法和替代实现:")
        all_match = True
        
        for batch_size, max_decode_seq_len in test_cases:
            original = mock._get_graph_type_and_seq_limit(batch_size, max_decode_seq_len)
            alternative = mock.alternative_implementation(batch_size, max_decode_seq_len)
            
            match = original == alternative
            all_match = all_match and match
            
            print(f"BS={batch_size:3d}, SeqLen={max_decode_seq_len:5d}: "
                  f"原始={original}, 替代={alternative}, 匹配={'✅' if match else '❌'}")
        
        return all_match
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def main():
    print("=" * 80)
    print("检查 _get_graph_type_and_seq_limit 方法是否为冗余代码")
    print("=" * 80)
    
    # 1. 扫描代码库查找使用
    print("\n1. 扫描代码库查找方法使用...")
    usage_files = scan_codebase_for_method_usage()
    
    if usage_files:
        print(f"❌ 找到 {len(usage_files)} 个文件使用了该方法:")
        for file_path in usage_files:
            print(f"   - {file_path}")
    else:
        print("✅ 没有找到任何文件使用该方法")
    
    # 2. 测试功能等价性
    print("\n2. 测试功能等价性...")
    functionality_match = test_method_functionality()
    
    if functionality_match:
        print("✅ 该方法的功能可以完全被替代实现")
    else:
        print("❌ 该方法有独特的功能")
    
    # 3. 结论
    print("\n" + "=" * 80)
    print("结论:")
    
    if not usage_files and functionality_match:
        print("✅ 确认: _get_graph_type_and_seq_limit 是冗余代码")
        print("   - 没有任何地方调用该方法")
        print("   - 其功能可以通过现有方法完全实现")
        print("   - 建议删除该方法以简化代码")
    else:
        print("❌ 该方法可能不是冗余代码")
        if usage_files:
            print("   - 发现有文件使用该方法")
        if not functionality_match:
            print("   - 该方法有独特的功能")

if __name__ == "__main__":
    main()
